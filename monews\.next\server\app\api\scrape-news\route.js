/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scrape-news/route";
exports.ids = ["app/api/scrape-news/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape-news%2Froute&page=%2Fapi%2Fscrape-news%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape-news%2Froute.ts&appDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape-news%2Froute&page=%2Fapi%2Fscrape-news%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape-news%2Froute.ts&appDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yaser_Documents_augment_projects_monews_monews_src_app_api_scrape_news_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/scrape-news/route.ts */ \"(rsc)/./src/app/api/scrape-news/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scrape-news/route\",\n        pathname: \"/api/scrape-news\",\n        filename: \"route\",\n        bundlePath: \"app/api/scrape-news/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\api\\\\scrape-news\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yaser_Documents_augment_projects_monews_monews_src_app_api_scrape_news_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape-news%2Froute&page=%2Fapi%2Fscrape-news%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape-news%2Froute.ts&appDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scrape-news/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/scrape-news/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\n\n\n// Keywords to identify Iraq-related news\nconst iraqKeywords = [\n    'العراق',\n    'بغداد',\n    'البصرة',\n    'نينوى',\n    'أربيل',\n    'النجف',\n    'كربلاء',\n    'الأنبار',\n    'ديالى',\n    'كركوك',\n    'بابل',\n    'ذي قار',\n    'واسط',\n    'صلاح الدين',\n    'القادسية',\n    'ميسان',\n    'المثنى',\n    'دهوك',\n    'السليمانية',\n    'عراقي',\n    'عراقية'\n];\n// Keywords to exclude (sports, entertainment, weather)\nconst excludeKeywords = [\n    'رياضة',\n    'كرة',\n    'فوتبول',\n    'كأس',\n    'دوري',\n    'مباراة',\n    'لاعب',\n    'مدرب',\n    'فن',\n    'فنان',\n    'فنانة',\n    'مسرح',\n    'سينما',\n    'أغنية',\n    'مطرب',\n    'مطربة',\n    'طقس',\n    'أمطار',\n    'حرارة',\n    'رياح',\n    'غيوم',\n    'مناخ'\n];\nfunction isIraqRelated(text) {\n    const lowerText = text.toLowerCase();\n    return iraqKeywords.some((keyword)=>lowerText.includes(keyword));\n}\nfunction shouldExclude(text) {\n    const lowerText = text.toLowerCase();\n    return excludeKeywords.some((keyword)=>lowerText.includes(keyword));\n}\nfunction extractProvince(text) {\n    const provinces = [\n        'بغداد',\n        'البصرة',\n        'نينوى',\n        'أربيل',\n        'النجف',\n        'كربلاء',\n        'الأنبار',\n        'ديالى',\n        'كركوك',\n        'بابل',\n        'ذي قار',\n        'واسط',\n        'صلاح الدين',\n        'القادسية',\n        'ميسان',\n        'المثنى',\n        'دهوك',\n        'السليمانية'\n    ];\n    for (const province of provinces){\n        if (text.includes(province)) {\n            return province;\n        }\n    }\n    return null;\n}\nasync function scrapeWebsite(url, sourceName) {\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(url, {\n            timeout: 15000,\n            headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n            }\n        });\n        const $ = cheerio__WEBPACK_IMPORTED_MODULE_2__.load(response.data);\n        const articles = [];\n        // Generic selectors for common news website structures\n        const selectors = [\n            'article',\n            '.article',\n            '.news-item',\n            '.post',\n            '.entry',\n            '[class*=\"article\"]',\n            '[class*=\"news\"]',\n            '[class*=\"post\"]'\n        ];\n        for (const selector of selectors){\n            $(selector).each((index, element)=>{\n                const $element = $(element);\n                // Try to find title\n                const titleSelectors = [\n                    'h1',\n                    'h2',\n                    'h3',\n                    '.title',\n                    '[class*=\"title\"]',\n                    'a'\n                ];\n                let title = '';\n                for (const titleSel of titleSelectors){\n                    const titleEl = $element.find(titleSel).first();\n                    if (titleEl.length && titleEl.text().trim()) {\n                        title = titleEl.text().trim();\n                        break;\n                    }\n                }\n                if (!title || title.length < 10) return;\n                // Try to find link\n                let link = '';\n                const linkEl = $element.find('a').first();\n                if (linkEl.length) {\n                    link = linkEl.attr('href') || '';\n                    if (link && !link.startsWith('http')) {\n                        const baseUrl = new URL(url).origin;\n                        link = new URL(link, baseUrl).href;\n                    }\n                }\n                // Try to find content/description\n                let content = '';\n                const contentSelectors = [\n                    '.content',\n                    '.description',\n                    '.excerpt',\n                    'p'\n                ];\n                for (const contentSel of contentSelectors){\n                    const contentEl = $element.find(contentSel).first();\n                    if (contentEl.length && contentEl.text().trim()) {\n                        content = contentEl.text().trim();\n                        break;\n                    }\n                }\n                const fullText = `${title} ${content}`;\n                // Check if Iraq-related and not excluded\n                if (isIraqRelated(fullText) && !shouldExclude(fullText)) {\n                    articles.push({\n                        title,\n                        content: content || null,\n                        url: link || url,\n                        source_name: sourceName,\n                        province: extractProvince(fullText),\n                        published_date: new Date().toISOString(),\n                        is_iraq_related: true\n                    });\n                }\n            });\n            if (articles.length > 0) break; // Stop if we found articles with this selector\n        }\n        return articles.slice(0, 20) // Limit to 20 articles per source\n        ;\n    } catch (error) {\n        console.error(`Error scraping ${url}:`, error);\n        return [];\n    }\n}\nasync function POST(request) {\n    try {\n        const { sourceId } = await request.json();\n        // Get active sources\n        let sourcesQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('news_sources').select('*').eq('is_active', true);\n        if (sourceId) {\n            sourcesQuery = sourcesQuery.eq('id', sourceId);\n        }\n        const { data: sources, error: sourcesError } = await sourcesQuery;\n        if (sourcesError) {\n            throw sourcesError;\n        }\n        if (!sources || sources.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'No active sources found'\n            });\n        }\n        let totalArticles = 0;\n        const results = [];\n        for (const source of sources){\n            try {\n                console.log(`Scraping ${source.name}...`);\n                const articles = await scrapeWebsite(source.url, source.name);\n                if (articles.length > 0) {\n                    // Add source_id to articles\n                    const articlesWithSourceId = articles.map((article)=>({\n                            ...article,\n                            source_id: source.id\n                        }));\n                    // Insert articles into database\n                    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('news_articles').insert(articlesWithSourceId).select();\n                    if (error) {\n                        console.error(`Error inserting articles for ${source.name}:`, error);\n                    } else {\n                        totalArticles += data?.length || 0;\n                    }\n                }\n                // Update source last_checked and status\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('news_sources').update({\n                    last_checked: new Date().toISOString(),\n                    status: articles.length > 0 ? 'active' : 'inactive'\n                }).eq('id', source.id);\n                results.push({\n                    source: source.name,\n                    articles: articles.length,\n                    status: 'success'\n                });\n            } catch (error) {\n                console.error(`Error processing source ${source.name}:`, error);\n                // Update source status to error\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('news_sources').update({\n                    last_checked: new Date().toISOString(),\n                    status: 'error'\n                }).eq('id', source.id);\n                results.push({\n                    source: source.name,\n                    articles: 0,\n                    status: 'error',\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            totalArticles,\n            results\n        });\n    } catch (error) {\n        console.error('Error in scrape-news API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scrape-news/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupabaseConfigured: () => (/* binding */ isSupabaseConfigured),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if we have valid Supabase credentials\nconst supabaseUrl = \"your_supabase_url_here\";\nconst supabaseAnonKey = \"your_supabase_anon_key_here\";\n// Use placeholder values if credentials are not set or are placeholder text\nconst isValidUrl = supabaseUrl && supabaseUrl !== 'your_supabase_url_here' && supabaseUrl.startsWith('https://');\nconst isValidKey = supabaseAnonKey && supabaseAnonKey !== 'your_supabase_anon_key_here';\nconst finalUrl = isValidUrl ? supabaseUrl : 'https://placeholder.supabase.co';\nconst finalKey = isValidKey ? supabaseAnonKey : 'placeholder-key';\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(finalUrl, finalKey);\n// Flag to check if Supabase is properly configured\nconst isSupabaseConfigured = isValidUrl && isValidKey;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/undici","vendor-chunks/axios","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/asynckit","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/whatwg-mimetype","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/nth-check","vendor-chunks/htmlparser2","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/get-proto","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape-news%2Froute&page=%2Fapi%2Fscrape-news%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape-news%2Froute.ts&appDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyaser%5CDocuments%5Caugment-projects%5Cmonews%5Cmonews&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();