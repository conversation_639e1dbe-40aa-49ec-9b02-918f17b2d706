import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "الرصد الإعلامي - Media Monitoring",
  description: "تطبيق الرصد الإعلامي لمتابعة الأخبار العراقية من مختلف المصادر",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  );
}
