"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [articles, setArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedProvince, setSelectedProvince] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchArticles();\n        }\n    }[\"Home.useEffect\"], [\n        selectedProvince\n    ]);\n    const fetchArticles = async ()=>{\n        setLoading(true);\n        // Check if Supabase is configured\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured) {\n            console.log('Supabase not configured, using demo data');\n            // Set demo data for development\n            setArticles([\n                {\n                    id: '1',\n                    title: 'مرحباً بك في تطبيق الرصد الإعلامي',\n                    content: 'هذا مثال على خبر تجريبي. يرجى تكوين Supabase لعرض الأخبار الحقيقية.',\n                    url: '#',\n                    source_id: '1',\n                    source_name: 'مصدر تجريبي',\n                    province: 'بغداد',\n                    published_date: new Date().toISOString(),\n                    scraped_at: new Date().toISOString(),\n                    is_iraq_related: true,\n                    category: 'تجريبي',\n                    created_at: new Date().toISOString()\n                }\n            ]);\n            setLoading(false);\n            return;\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_articles').select('*').eq('is_iraq_related', true).order('published_date', {\n                ascending: false\n            }).limit(50);\n            if (selectedProvince) {\n                query = query.eq('province', selectedProvince);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching articles:', error);\n            } else {\n                setArticles(data || []);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const provinces = [\n        'بغداد',\n        'البصرة',\n        'نينوى',\n        'أربيل',\n        'النجف',\n        'كربلاء',\n        'الأنبار',\n        'ديالى',\n        'كركوك',\n        'بابل',\n        'ذي قار',\n        'واسط',\n        'صلاح الدين',\n        'القادسية',\n        'ميسان',\n        'المثنى',\n        'دهوك',\n        'السليمانية'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"الرصد الإعلامي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                        children: \"الأخبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/sources\",\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"إدارة المصادر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"فلترة حسب المحافظة:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedProvince,\n                                    onChange: (e)=>setSelectedProvince(e.target.value),\n                                    className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"جميع المحافظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        provinces.map((province)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: province,\n                                                children: province\n                                            }, province, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                        children: articles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3 line-clamp-2\",\n                                            children: article.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_4__.format)(new Date(article.published_date), 'dd MMMM yyyy - HH:mm', {\n                                                            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.ar\n                                                        })\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this),\n                                                article.province && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        article.province\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        article.source_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this),\n                                        article.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 text-sm mb-4 line-clamp-3\",\n                                            children: [\n                                                article.content.substring(0, 150),\n                                                \"...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: article.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                            children: [\n                                                \"قراءة المزيد\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this)\n                            }, article.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    !loading && articles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد أخبار متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"ITNK+Fnw0PulNixrDA/PZ97DG5k=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});