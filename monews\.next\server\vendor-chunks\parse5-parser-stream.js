"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse5-parser-stream";
exports.ids = ["vendor-chunks/parse5-parser-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse5-parser-stream/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/parse5-parser-stream/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParserStream: () => (/* binding */ ParserStream)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n\n\n/* eslint-disable unicorn/consistent-function-scoping -- The rule seems to be broken here. */\n/**\n * Streaming HTML parser with scripting support.\n * A [writable stream](https://nodejs.org/api/stream.html#stream_class_stream_writable).\n *\n * @example\n *\n * ```js\n * const ParserStream = require('parse5-parser-stream');\n * const http = require('http');\n * const { finished } = require('node:stream');\n *\n * // Fetch the page content and obtain it's <head> node\n * http.get('http://inikulin.github.io/parse5/', res => {\n *     const parser = new ParserStream();\n *\n *     finished(parser, () => {\n *         console.log(parser.document.childNodes[1].childNodes[0].tagName); //> 'head'\n *     });\n *\n *     res.pipe(parser);\n * });\n * ```\n *\n */\nclass ParserStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Writable {\n    static getFragmentStream(fragmentContext, options) {\n        const parser = parse5__WEBPACK_IMPORTED_MODULE_1__.Parser.getFragmentParser(fragmentContext, options);\n        const stream = new ParserStream(options, parser);\n        return stream;\n    }\n    /** The resulting document node. */\n    get document() {\n        return this.parser.document;\n    }\n    getFragment() {\n        return this.parser.getFragment();\n    }\n    /**\n     * @param options Parsing options.\n     */\n    constructor(options, parser = new parse5__WEBPACK_IMPORTED_MODULE_1__.Parser(options)) {\n        super({ decodeStrings: false });\n        this.parser = parser;\n        this.lastChunkWritten = false;\n        this.writeCallback = undefined;\n        this.pendingHtmlInsertions = [];\n        const resume = () => {\n            for (let i = this.pendingHtmlInsertions.length - 1; i >= 0; i--) {\n                this.parser.tokenizer.insertHtmlAtCurrentPos(this.pendingHtmlInsertions[i]);\n            }\n            this.pendingHtmlInsertions.length = 0;\n            //NOTE: keep parsing if we don't wait for the next input chunk\n            this.parser.tokenizer.resume(this.writeCallback);\n        };\n        const documentWrite = (html) => {\n            if (!this.parser.stopped) {\n                this.pendingHtmlInsertions.push(html);\n            }\n        };\n        const scriptHandler = (scriptElement) => {\n            if (this.listenerCount('script') > 0) {\n                this.parser.tokenizer.pause();\n                this.emit('script', scriptElement, documentWrite, resume);\n            }\n        };\n        this.parser.scriptHandler = scriptHandler;\n    }\n    //WritableStream implementation\n    _write(chunk, _encoding, callback) {\n        if (typeof chunk !== 'string') {\n            throw new TypeError('Parser can work only with string streams.');\n        }\n        this.writeCallback = callback;\n        this.parser.tokenizer.write(chunk, this.lastChunkWritten, this.writeCallback);\n    }\n    // TODO [engine:node@>=16]: Due to issues with Node < 16, we are overriding `end` instead of `_final`.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    end(chunk, encoding, callback) {\n        this.lastChunkWritten = true;\n        super.end(chunk || '', encoding, callback);\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse5-parser-stream/dist/index.js\n");

/***/ })

};
;