"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupabaseConfigured: () => (/* binding */ isSupabaseConfigured),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if we have valid Supabase credentials\nconst supabaseUrl = \"your_supabase_url_here\";\nconst supabaseAnonKey = \"your_supabase_anon_key_here\";\n// Use placeholder values if credentials are not set or are placeholder text\nconst isValidUrl = supabaseUrl && supabaseUrl !== 'your_supabase_url_here' && supabaseUrl.startsWith('https://');\nconst isValidKey = supabaseAnonKey && supabaseAnonKey !== 'your_supabase_anon_key_here';\nconst finalUrl = isValidUrl ? supabaseUrl : 'https://placeholder.supabase.co';\nconst finalKey = isValidKey ? supabaseAnonKey : 'placeholder-key';\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(finalUrl, finalKey);\n// Flag to check if Supabase is properly configured\nconst isSupabaseConfigured = isValidUrl && isValidKey;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});