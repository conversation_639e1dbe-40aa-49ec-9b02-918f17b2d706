import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json()

    if (!url) {
      return NextResponse.json(
        { success: false, error: 'URL is required' },
        { status: 400 }
      )
    }

    // Test the URL accessibility
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    if (response.status === 200) {
      return NextResponse.json({
        success: true,
        message: 'Source is accessible',
        status: response.status
      })
    } else {
      return NextResponse.json({
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`
      })
    }
  } catch (error: any) {
    console.error('Error testing source:', error)
    
    let errorMessage = 'Unknown error'
    if (error.code === 'ENOTFOUND') {
      errorMessage = 'Domain not found'
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused'
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Connection timeout'
    } else if (error.response) {
      errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`
    } else if (error.message) {
      errorMessage = error.message
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    })
  }
}
