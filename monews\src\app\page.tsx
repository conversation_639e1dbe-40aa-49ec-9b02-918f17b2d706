'use client'

import { useState, useEffect } from 'react'
import { supabase, NewsArticle, isSupabaseConfigured } from '@/lib/supabase'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { ExternalLink, Calendar, MapPin, Globe, AlertTriangle } from 'lucide-react'

export default function Home() {
  const [articles, setArticles] = useState<NewsArticle[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedProvince, setSelectedProvince] = useState<string>('')

  useEffect(() => {
    fetchArticles()
  }, [selectedProvince])

  const fetchArticles = async () => {
    setLoading(true)

    // Check if Supabase is configured
    if (!isSupabaseConfigured) {
      console.log('Supabase not configured, using demo data')
      // Set demo data for development
      setArticles([
        {
          id: '1',
          title: 'مرحباً بك في تطبيق الرصد الإعلامي',
          content: 'هذا مثال على خبر تجريبي. يرجى تكوين Supabase لعرض الأخبار الحقيقية.',
          url: '#',
          source_id: '1',
          source_name: 'مصدر تجريبي',
          province: 'بغداد',
          published_date: new Date().toISOString(),
          scraped_at: new Date().toISOString(),
          is_iraq_related: true,
          category: 'تجريبي',
          created_at: new Date().toISOString()
        }
      ])
      setLoading(false)
      return
    }

    try {
      let query = supabase
        .from('news_articles')
        .select('*')
        .eq('is_iraq_related', true)
        .order('published_date', { ascending: false })
        .limit(50)

      if (selectedProvince) {
        query = query.eq('province', selectedProvince)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching articles:', error)
      } else {
        setArticles(data || [])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const provinces = [
    'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار',
    'ديالى', 'كركوك', 'بابل', 'ذي قار', 'واسط', 'صلاح الدين', 'القادسية',
    'ميسان', 'المثنى', 'دهوك', 'السليمانية'
  ]

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">الرصد الإعلامي</h1>
            <nav className="flex space-x-4 space-x-reverse">
              <a href="/" className="text-blue-600 hover:text-blue-800 font-medium">
                الأخبار
              </a>
              <a href="/sources" className="text-gray-600 hover:text-gray-800">
                إدارة المصادر
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Configuration Warning */}
        {!isSupabaseConfigured && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="w-5 h-5 text-yellow-400 ml-2" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  تحتاج إلى تكوين قاعدة البيانات
                </h3>
                <p className="mt-1 text-sm text-yellow-700">
                  يرجى تكوين Supabase في ملف .env.local لعرض الأخبار الحقيقية. راجع ملف README.md للتعليمات.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-4 items-center">
            <label className="text-sm font-medium text-gray-700">
              فلترة حسب المحافظة:
            </label>
            <select
              value={selectedProvince}
              onChange={(e) => setSelectedProvince(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع المحافظات</option>
              {provinces.map((province) => (
                <option key={province} value={province}>
                  {province}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Articles Grid */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {articles.map((article) => (
              <article
                key={article.id}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">
                    {article.title}
                  </h2>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 ml-2" />
                      {format(new Date(article.published_date), 'dd MMMM yyyy - HH:mm', { locale: ar })}
                    </div>

                    {article.province && (
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="w-4 h-4 ml-2" />
                        {article.province}
                      </div>
                    )}

                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="w-4 h-4 ml-2" />
                      {article.source_name}
                    </div>
                  </div>

                  {article.content && (
                    <p className="text-gray-700 text-sm mb-4 line-clamp-3">
                      {article.content.substring(0, 150)}...
                    </p>
                  )}

                  <a
                    href={article.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    قراءة المزيد
                    <ExternalLink className="w-4 h-4 mr-2" />
                  </a>
                </div>
              </article>
            ))}
          </div>
        )}

        {!loading && articles.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">لا توجد أخبار متاحة حالياً</p>
          </div>
        )}
      </main>
    </div>
  )
}
