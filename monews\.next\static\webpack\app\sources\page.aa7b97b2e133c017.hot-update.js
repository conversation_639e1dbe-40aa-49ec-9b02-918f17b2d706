"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sources/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TriangleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"triangle-alert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/sources/page.tsx":
/*!**********************************!*\
  !*** ./src/app/sources/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SourcesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_SourceForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SourceForm */ \"(app-pages-browser)/./src/components/SourceForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SourcesPage() {\n    _s();\n    const [sources, setSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingSource, setEditingSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [scraping, setScraping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrapingSource, setScrapingSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SourcesPage.useEffect\": ()=>{\n            fetchSources();\n        }\n    }[\"SourcesPage.useEffect\"], [\n        selectedCategory\n    ]);\n    const fetchSources = async ()=>{\n        setLoading(true);\n        // Check if Supabase is configured\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured) {\n            console.log('Supabase not configured, using demo data');\n            // Set demo data for development\n            setSources([\n                {\n                    id: '1',\n                    name: 'مصدر تجريبي',\n                    url: 'https://example.com',\n                    category: 'channel',\n                    is_active: true,\n                    last_checked: new Date().toISOString(),\n                    status: 'active',\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }\n            ]);\n            setLoading(false);\n            return;\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_sources').select('*').order('created_at', {\n                ascending: false\n            });\n            if (selectedCategory) {\n                query = query.eq('category', selectedCategory);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching sources:', error);\n            } else {\n                setSources(data || []);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const categories = [\n        {\n            value: 'channel',\n            label: 'قنوات'\n        },\n        {\n            value: 'agency',\n            label: 'وكالات'\n        },\n        {\n            value: 'newspaper',\n            label: 'صحف'\n        },\n        {\n            value: 'facebook',\n            label: 'فيسبوك'\n        },\n        {\n            value: 'telegram',\n            label: 'تليغرام'\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'active':\n                return 'نشط';\n            case 'inactive':\n                return 'غير نشط';\n            case 'error':\n                return 'خطأ';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const getCategoryText = (category)=>{\n        const cat = categories.find((c)=>c.value === category);\n        return cat ? cat.label : category;\n    };\n    const deleteSource = async (sourceId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المصدر؟')) return;\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_sources').delete().eq('id', sourceId);\n            if (error) throw error;\n            fetchSources();\n        } catch (error) {\n            console.error('Error deleting source:', error);\n            alert('حدث خطأ أثناء حذف المصدر');\n        }\n    };\n    const scrapeNews = async (sourceId)=>{\n        setScraping(true);\n        setScrapingSource(sourceId || null);\n        try {\n            const response = await fetch('/api/scrape-news', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    sourceId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"تم جلب \".concat(result.totalArticles, \" خبر بنجاح\"));\n                fetchSources() // Refresh to update last_checked times\n                ;\n            } else {\n                alert(\"خطأ: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Error scraping news:', error);\n            alert('حدث خطأ أثناء جلب الأخبار');\n        } finally{\n            setScraping(false);\n            setScrapingSource(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"إدارة المصادر الإخبارية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"الأخبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/sources\",\n                                        className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                        children: \"إدارة المصادر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    !_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-400 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"تحتاج إلى تكوين قاعدة البيانات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-yellow-700\",\n                                            children: \"يرجى تكوين Supabase في ملف .env.local لاستخدام جميع الميزات. راجع ملف README.md للتعليمات.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4 items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"فلترة حسب النوع:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"جميع الأنواع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category.value,\n                                                    children: category.label\n                                                }, category.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrapeNews(),\n                                        disabled: scraping,\n                                        className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50\",\n                                        children: [\n                                            scraping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            scraping ? 'جاري الجلب...' : 'جلب الأخبار'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة مصدر جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-md rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"اسم المصدر\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"النوع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"آخر فحص\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الإجراءات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: sources.map((source)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: source.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 truncate max-w-xs\",\n                                                                children: source.url\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                        children: getCategoryText(source.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getStatusIcon(source.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2 text-sm text-gray-900\",\n                                                                children: getStatusText(source.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                    children: source.last_checked ? new Date(source.last_checked).toLocaleDateString('ar-EG') : 'لم يتم الفحص'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>scrapeNews(source.id),\n                                                                disabled: scraping,\n                                                                className: \"text-green-600 hover:text-green-900 disabled:opacity-50\",\n                                                                title: \"جلب الأخبار من هذا المصدر\",\n                                                                children: scrapingSource === source.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingSource(source),\n                                                                className: \"text-blue-600 hover:text-blue-900\",\n                                                                title: \"تعديل المصدر\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>deleteSource(source.id),\n                                                                className: \"text-red-600 hover:text-red-900\",\n                                                                title: \"حذف المصدر\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, source.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    !loading && sources.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد مصادر متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            (showAddForm || editingSource) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SourceForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                source: editingSource,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingSource(null);\n                },\n                onSave: ()=>{\n                    fetchSources();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(SourcesPage, \"r05u7DNHs5QAL6ISbR2qW3xzP38=\");\n_c = SourcesPage;\nvar _c;\n$RefreshReg$(_c, \"SourcesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sources/page.tsx\n"));

/***/ })

});