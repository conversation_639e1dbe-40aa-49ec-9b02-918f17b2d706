"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sources/page",{

/***/ "(app-pages-browser)/./src/app/sources/page.tsx":
/*!**********************************!*\
  !*** ./src/app/sources/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SourcesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,Edit,Globe,Plus,RefreshCw,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_SourceForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SourceForm */ \"(app-pages-browser)/./src/components/SourceForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SourcesPage() {\n    _s();\n    const [sources, setSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingSource, setEditingSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [scraping, setScraping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrapingSource, setScrapingSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SourcesPage.useEffect\": ()=>{\n            fetchSources();\n        }\n    }[\"SourcesPage.useEffect\"], [\n        selectedCategory\n    ]);\n    const fetchSources = async ()=>{\n        setLoading(true);\n        // Check if Supabase is configured\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured) {\n            console.log('Supabase not configured, using demo data');\n            // Set demo data for development\n            setSources([\n                {\n                    id: '1',\n                    name: 'مصدر تجريبي',\n                    url: 'https://example.com',\n                    category: 'channel',\n                    is_active: true,\n                    last_checked: new Date().toISOString(),\n                    status: 'active',\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                }\n            ]);\n            setLoading(false);\n            return;\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_sources').select('*').order('created_at', {\n                ascending: false\n            });\n            if (selectedCategory) {\n                query = query.eq('category', selectedCategory);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching sources:', error);\n            } else {\n                setSources(data || []);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const categories = [\n        {\n            value: 'channel',\n            label: 'قنوات'\n        },\n        {\n            value: 'agency',\n            label: 'وكالات'\n        },\n        {\n            value: 'newspaper',\n            label: 'صحف'\n        },\n        {\n            value: 'facebook',\n            label: 'فيسبوك'\n        },\n        {\n            value: 'telegram',\n            label: 'تليغرام'\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'active':\n                return 'نشط';\n            case 'inactive':\n                return 'غير نشط';\n            case 'error':\n                return 'خطأ';\n            default:\n                return 'غير محدد';\n        }\n    };\n    const getCategoryText = (category)=>{\n        const cat = categories.find((c)=>c.value === category);\n        return cat ? cat.label : category;\n    };\n    const deleteSource = async (sourceId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المصدر؟')) return;\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_sources').delete().eq('id', sourceId);\n            if (error) throw error;\n            fetchSources();\n        } catch (error) {\n            console.error('Error deleting source:', error);\n            alert('حدث خطأ أثناء حذف المصدر');\n        }\n    };\n    const scrapeNews = async (sourceId)=>{\n        setScraping(true);\n        setScrapingSource(sourceId || null);\n        try {\n            const response = await fetch('/api/scrape-news', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    sourceId\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"تم جلب \".concat(result.totalArticles, \" خبر بنجاح\"));\n                fetchSources() // Refresh to update last_checked times\n                ;\n            } else {\n                alert(\"خطأ: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('Error scraping news:', error);\n            alert('حدث خطأ أثناء جلب الأخبار');\n        } finally{\n            setScraping(false);\n            setScrapingSource(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"إدارة المصادر الإخبارية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"الأخبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/sources\",\n                                        className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                        children: \"إدارة المصادر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4 items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"فلترة حسب النوع:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"جميع الأنواع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category.value,\n                                                    children: category.label\n                                                }, category.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrapeNews(),\n                                        disabled: scraping,\n                                        className: \"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50\",\n                                        children: [\n                                            scraping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            scraping ? 'جاري الجلب...' : 'جلب الأخبار'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة مصدر جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-md rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"اسم المصدر\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"النوع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"آخر فحص\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الإجراءات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: sources.map((source)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: source.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 truncate max-w-xs\",\n                                                                children: source.url\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                        children: getCategoryText(source.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getStatusIcon(source.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2 text-sm text-gray-900\",\n                                                                children: getStatusText(source.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                    children: source.last_checked ? new Date(source.last_checked).toLocaleDateString('ar-EG') : 'لم يتم الفحص'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>scrapeNews(source.id),\n                                                                disabled: scraping,\n                                                                className: \"text-green-600 hover:text-green-900 disabled:opacity-50\",\n                                                                title: \"جلب الأخبار من هذا المصدر\",\n                                                                children: scrapingSource === source.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingSource(source),\n                                                                className: \"text-blue-600 hover:text-blue-900\",\n                                                                title: \"تعديل المصدر\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>deleteSource(source.id),\n                                                                className: \"text-red-600 hover:text-red-900\",\n                                                                title: \"حذف المصدر\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_Edit_Globe_Plus_RefreshCw_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, source.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    !loading && sources.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد مصادر متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            (showAddForm || editingSource) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SourceForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                source: editingSource,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingSource(null);\n                },\n                onSave: ()=>{\n                    fetchSources();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\sources\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(SourcesPage, \"r05u7DNHs5QAL6ISbR2qW3xzP38=\");\n_c = SourcesPage;\nvar _c;\n$RefreshReg$(_c, \"SourcesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sources/page.tsx\n"));

/***/ })

});