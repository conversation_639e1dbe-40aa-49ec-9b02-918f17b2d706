import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import axios from 'axios'
import * as cheerio from 'cheerio'

// Keywords to identify Iraq-related news
const iraqKeywords = [
  'العراق', 'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار',
  'ديالى', 'كركوك', 'بابل', 'ذي قار', 'واسط', 'صلاح الدين', 'القادسية',
  'ميسان', 'المثنى', 'دهوك', 'السليمانية', 'عراقي', 'عراقية'
]

// Keywords to exclude (sports, entertainment, weather)
const excludeKeywords = [
  'رياضة', 'كرة', 'فوتبول', 'كأس', 'دوري', 'مباراة', 'لاعب', 'مدرب',
  'فن', 'فنان', 'فنانة', 'مسرح', 'سينما', 'أغنية', 'مطرب', 'مطربة',
  'طقس', 'أمطار', 'حرارة', 'رياح', 'غيوم', 'مناخ'
]

function isIraqRelated(text: string): boolean {
  const lowerText = text.toLowerCase()
  return iraqKeywords.some(keyword => lowerText.includes(keyword))
}

function shouldExclude(text: string): boolean {
  const lowerText = text.toLowerCase()
  return excludeKeywords.some(keyword => lowerText.includes(keyword))
}

function extractProvince(text: string): string | null {
  const provinces = [
    'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار',
    'ديالى', 'كركوك', 'بابل', 'ذي قار', 'واسط', 'صلاح الدين', 'القادسية',
    'ميسان', 'المثنى', 'دهوك', 'السليمانية'
  ]
  
  for (const province of provinces) {
    if (text.includes(province)) {
      return province
    }
  }
  return null
}

async function scrapeWebsite(url: string, sourceName: string): Promise<any[]> {
  try {
    const response = await axios.get(url, {
      timeout: 15000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    const $ = cheerio.load(response.data)
    const articles: any[] = []

    // Generic selectors for common news website structures
    const selectors = [
      'article',
      '.article',
      '.news-item',
      '.post',
      '.entry',
      '[class*="article"]',
      '[class*="news"]',
      '[class*="post"]'
    ]

    for (const selector of selectors) {
      $(selector).each((index, element) => {
        const $element = $(element)
        
        // Try to find title
        const titleSelectors = ['h1', 'h2', 'h3', '.title', '[class*="title"]', 'a']
        let title = ''
        
        for (const titleSel of titleSelectors) {
          const titleEl = $element.find(titleSel).first()
          if (titleEl.length && titleEl.text().trim()) {
            title = titleEl.text().trim()
            break
          }
        }

        if (!title || title.length < 10) return

        // Try to find link
        let link = ''
        const linkEl = $element.find('a').first()
        if (linkEl.length) {
          link = linkEl.attr('href') || ''
          if (link && !link.startsWith('http')) {
            const baseUrl = new URL(url).origin
            link = new URL(link, baseUrl).href
          }
        }

        // Try to find content/description
        let content = ''
        const contentSelectors = ['.content', '.description', '.excerpt', 'p']
        for (const contentSel of contentSelectors) {
          const contentEl = $element.find(contentSel).first()
          if (contentEl.length && contentEl.text().trim()) {
            content = contentEl.text().trim()
            break
          }
        }

        const fullText = `${title} ${content}`
        
        // Check if Iraq-related and not excluded
        if (isIraqRelated(fullText) && !shouldExclude(fullText)) {
          articles.push({
            title,
            content: content || null,
            url: link || url,
            source_name: sourceName,
            province: extractProvince(fullText),
            published_date: new Date().toISOString(),
            is_iraq_related: true
          })
        }
      })

      if (articles.length > 0) break // Stop if we found articles with this selector
    }

    return articles.slice(0, 20) // Limit to 20 articles per source
  } catch (error) {
    console.error(`Error scraping ${url}:`, error)
    return []
  }
}

export async function POST(request: NextRequest) {
  try {
    const { sourceId } = await request.json()

    // Get active sources
    let sourcesQuery = supabase
      .from('news_sources')
      .select('*')
      .eq('is_active', true)

    if (sourceId) {
      sourcesQuery = sourcesQuery.eq('id', sourceId)
    }

    const { data: sources, error: sourcesError } = await sourcesQuery

    if (sourcesError) {
      throw sourcesError
    }

    if (!sources || sources.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No active sources found'
      })
    }

    let totalArticles = 0
    const results = []

    for (const source of sources) {
      try {
        console.log(`Scraping ${source.name}...`)
        
        const articles = await scrapeWebsite(source.url, source.name)
        
        if (articles.length > 0) {
          // Add source_id to articles
          const articlesWithSourceId = articles.map(article => ({
            ...article,
            source_id: source.id
          }))

          // Insert articles into database
          const { data, error } = await supabase
            .from('news_articles')
            .insert(articlesWithSourceId)
            .select()

          if (error) {
            console.error(`Error inserting articles for ${source.name}:`, error)
          } else {
            totalArticles += data?.length || 0
          }
        }

        // Update source last_checked and status
        await supabase
          .from('news_sources')
          .update({
            last_checked: new Date().toISOString(),
            status: articles.length > 0 ? 'active' : 'inactive'
          })
          .eq('id', source.id)

        results.push({
          source: source.name,
          articles: articles.length,
          status: 'success'
        })

      } catch (error) {
        console.error(`Error processing source ${source.name}:`, error)
        
        // Update source status to error
        await supabase
          .from('news_sources')
          .update({
            last_checked: new Date().toISOString(),
            status: 'error'
          })
          .eq('id', source.id)

        results.push({
          source: source.name,
          articles: 0,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      totalArticles,
      results
    })

  } catch (error) {
    console.error('Error in scrape-news API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
