# الرصد الإعلامي - Media Monitoring

تطبيق ويب لمراقبة وجمع الأخبار العراقية من مختلف المصادر الإعلامية بشكل تلقائي.

## الميزات

- **عرض الأخبار**: عرض الأخبار مع التفاصيل (العنوان، التاريخ، المحافظة، المصدر)
- **إدارة المصادر**: إضافة وتعديل وحذف المصادر الإخبارية
- **تصنيف المصادر**: قنوات، وكالات، صحف، فيسبوك، تليغرام
- **جلب تلقائي**: جلب الأخبار من المصادر بشكل تلقائي
- **فلترة ذكية**: عرض الأخبار العراقية فقط واستثناء أخبار الرياضة والفن والطقس
- **فلترة حسب المحافظة**: إمكانية فلترة الأخبار حسب المحافظات العراقية
- **واجهة عربية**: تصميم متجاوب يدعم اللغة العربية

## التقنيات المستخدمة

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Web Scraping**: Cheerio, Axios
- **Icons**: Lucide React
- **Date Handling**: date-fns

## إعداد المشروع

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. إعداد قاعدة البيانات

1. إنشاء حساب في [Supabase](https://supabase.com)
2. إنشاء مشروع جديد
3. تشغيل SQL الموجود في `database/schema.sql` في SQL Editor
4. نسخ URL و API Key من إعدادات المشروع

### 3. إعداد متغيرات البيئة

إنشاء ملف `.env.local` وإضافة:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 4. تشغيل التطبيق

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## استخدام التطبيق

### إضافة مصادر إخبارية

1. انتقل إلى صفحة "إدارة المصادر"
2. اضغط على "إضافة مصدر جديد"
3. أدخل اسم المصدر والرابط واختر النوع
4. يمكن اختبار المصدر قبل الحفظ

### جلب الأخبار

- **جلب من جميع المصادر**: اضغط على "جلب الأخبار" في صفحة إدارة المصادر
- **جلب من مصدر واحد**: اضغط على أيقونة التحميل بجانب المصدر المطلوب

### عرض الأخبار

- الصفحة الرئيسية تعرض آخر الأخبار
- يمكن فلترة الأخبار حسب المحافظة
- الضغط على "قراءة المزيد" يفتح الخبر في موقعه الأصلي

## هيكل المشروع

```
monews/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── scrape-news/     # API لجلب الأخبار
│   │   │   └── test-source/     # API لاختبار المصادر
│   │   ├── sources/             # صفحة إدارة المصادر
│   │   ├── layout.tsx           # التخطيط الرئيسي
│   │   └── page.tsx             # الصفحة الرئيسية
│   ├── components/
│   │   └── SourceForm.tsx       # مكون إضافة/تعديل المصادر
│   └── lib/
│       └── supabase.ts          # إعداد قاعدة البيانات
├── database/
│   └── schema.sql               # هيكل قاعدة البيانات
└── README.md
```

## قاعدة البيانات

### الجداول

- **news_sources**: المصادر الإخبارية
- **news_articles**: الأخبار المجمعة
- **categories**: تصنيفات الأخبار

### العلاقات

- كل خبر مرتبط بمصدر واحد
- المصادر مصنفة حسب النوع (قناة، وكالة، صحيفة، إلخ)

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.
