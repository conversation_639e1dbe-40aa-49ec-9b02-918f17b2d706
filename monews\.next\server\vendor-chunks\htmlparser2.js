"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/htmlparser2";
exports.ids = ["vendor-chunks/htmlparser2"];
exports.modules = {

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js":
/*!****************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Parser.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\n\nconst formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nconst pTag = new Set([\"p\"]);\nconst tableSectionTags = new Set([\"thead\", \"tbody\"]);\nconst ddtTags = new Set([\"dd\", \"dt\"]);\nconst rtpTags = new Set([\"rt\", \"rp\"]);\nconst openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nconst voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nconst foreignContextElements = new Set([\"math\", \"svg\"]);\nconst htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nconst reNameEnd = /\\s|\\//;\nclass Parser {\n    constructor(cbs, options = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.htmlMode = !this.options.xmlMode;\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : this.htmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : this.htmlMode;\n        this.recognizeSelfClosing =\n            (_c = options.recognizeSelfClosing) !== null && _c !== void 0 ? _c : !this.htmlMode;\n        this.tokenizer = new ((_d = options.Tokenizer) !== null && _d !== void 0 ? _d : _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.options, this);\n        this.foreignContext = [!this.htmlMode];\n        (_f = (_e = this.cbs).onparserinit) === null || _f === void 0 ? void 0 : _f.call(_e, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    ontext(start, endIndex) {\n        var _a, _b;\n        const data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    }\n    /** @internal */\n    ontextentity(cp, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp));\n        this.startIndex = endIndex;\n    }\n    /**\n     * Checks if the current tag is a void element. Override this if you want\n     * to specify your own additional void elements.\n     */\n    isVoidElement(name) {\n        return this.htmlMode && voidElements.has(name);\n    }\n    /** @internal */\n    onopentagname(start, endIndex) {\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    }\n    emitOpenTag(name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        const impliesClose = this.htmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 && impliesClose.has(this.stack[0])) {\n                const element = this.stack.shift();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.unshift(name);\n            if (this.htmlMode) {\n                if (foreignContextElements.has(name)) {\n                    this.foreignContext.unshift(true);\n                }\n                else if (htmlIntegrationElements.has(name)) {\n                    this.foreignContext.unshift(false);\n                }\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    }\n    endOpenTag(isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    }\n    /** @internal */\n    onopentagend(endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onclosetag(start, endIndex) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (this.htmlMode &&\n            (foreignContextElements.has(name) ||\n                htmlIntegrationElements.has(name))) {\n            this.foreignContext.shift();\n        }\n        if (!this.isVoidElement(name)) {\n            const pos = this.stack.indexOf(name);\n            if (pos !== -1) {\n                for (let index = 0; index <= pos; index++) {\n                    const element = this.stack.shift();\n                    // We know the stack has sufficient elements.\n                    (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, index !== pos);\n                }\n            }\n            else if (this.htmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (this.htmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\");\n            (_f = (_e = this.cbs).onopentag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", {}, true);\n            (_h = (_g = this.cbs).onclosetag) === null || _h === void 0 ? void 0 : _h.call(_g, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onselfclosingtag(endIndex) {\n        this.endIndex = endIndex;\n        if (this.recognizeSelfClosing || this.foreignContext[0]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    }\n    closeCurrentTag(isOpenImplied) {\n        var _a, _b;\n        const name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[0] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.shift();\n        }\n    }\n    /** @internal */\n    onattribname(start, endIndex) {\n        this.startIndex = start;\n        const name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    }\n    /** @internal */\n    onattribdata(start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    }\n    /** @internal */\n    onattribentity(cp) {\n        this.attribvalue += (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp);\n    }\n    /** @internal */\n    onattribend(quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Double\n            ? '\"'\n            : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Single\n                ? \"'\"\n                : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    }\n    getInstructionName(value) {\n        const index = value.search(reNameEnd);\n        let name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    }\n    /** @internal */\n    ondeclaration(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`!${name}`, `!${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onprocessinginstruction(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`?${name}`, `?${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncomment(start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncdata(start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex - offset);\n        if (!this.htmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, `[CDATA[${value}]]`);\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onend() {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (let index = 0; index < this.stack.length; index++) {\n                this.cbs.onclosetag(this.stack[index], true);\n            }\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    reset() {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.foreignContext.length = 0;\n        this.foreignContext.unshift(!this.htmlMode);\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    }\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    parseComplete(data) {\n        this.reset();\n        this.end(data);\n    }\n    getSlice(start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        let slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    }\n    shiftBuffer() {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    }\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    write(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    }\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    end(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    }\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    pause() {\n        this.tokenizer.pause();\n    }\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    resume() {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    }\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    parseChunk(chunk) {\n        this.write(chunk);\n    }\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    done(chunk) {\n        this.end(chunk);\n    }\n}\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Tokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuoteType: () => (/* binding */ QuoteType),\n/* harmony export */   \"default\": () => (/* binding */ Tokenizer)\n/* harmony export */ });\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"BeforeSpecialT\"] = 23] = \"BeforeSpecialT\";\n    State[State[\"SpecialStartSequence\"] = 24] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 25] = \"InSpecialTag\";\n    State[State[\"InEntity\"] = 26] = \"InEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType || (QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nconst Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]), // CDATA[\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]), // ]]>\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]), // `-->`\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]), // `</script`\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]), // `</style`\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n    TextareaEnd: new Uint8Array([\n        0x3c, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x61, 0x72, 0x65, 0x61,\n    ]), // `</textarea`\n};\nclass Tokenizer {\n    constructor({ xmlMode = false, decodeEntities = true, }, cbs) {\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** The start of the last entity. */\n        this.entityStart = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityDecoder = new entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.EntityDecoder(xmlMode ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.xmlDecodeTree : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.htmlDecodeTree, (cp, consumed) => this.emitCodePoint(cp, consumed));\n    }\n    reset() {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    }\n    write(chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    }\n    end() {\n        if (this.running)\n            this.finish();\n    }\n    pause() {\n        this.running = false;\n    }\n    resume() {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    }\n    stateText(c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.startEntity();\n        }\n    }\n    stateSpecialStartSequence(c) {\n        const isEnd = this.sequenceIndex === this.currentSequence.length;\n        const isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    }\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    stateInSpecialTag(c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                const endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    const actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.startEntity();\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    }\n    stateCDATASequence(c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    }\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    fastForwardTo(c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    }\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    stateInCommentLike(c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    }\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    isTagStartChar(c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    }\n    startSpecial(sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    }\n    stateBeforeTagName(c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            const lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (this.xmlMode) {\n                this.state = State.InTagName;\n            }\n            else if (lower === Sequences.ScriptEnd[2]) {\n                this.state = State.BeforeSpecialS;\n            }\n            else if (lower === Sequences.TitleEnd[2]) {\n                this.state = State.BeforeSpecialT;\n            }\n            else {\n                this.state = State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    }\n    stateInTagName(c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateBeforeClosingTagName(c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInClosingTagName(c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    }\n    stateAfterClosingTagName(c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeAttributeName(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInSelfClosingTag(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateInAttributeName(c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = this.index;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.sectionStart);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.sectionStart);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    }\n    handleInAttributeValue(c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index + 1);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.startEntity();\n        }\n    }\n    stateInAttributeValueDoubleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    }\n    stateInAttributeValueSingleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    }\n    stateInAttributeValueNoQuotes(c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.startEntity();\n        }\n    }\n    stateBeforeDeclaration(c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    }\n    stateInDeclaration(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateInProcessingInstruction(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeComment(c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    }\n    stateInSpecialComment(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeSpecialS(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    stateBeforeSpecialT(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.TitleEnd[3]) {\n            this.startSpecial(Sequences.TitleEnd, 4);\n        }\n        else if (lower === Sequences.TextareaEnd[3]) {\n            this.startSpecial(Sequences.TextareaEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    startEntity() {\n        this.baseState = this.state;\n        this.state = State.InEntity;\n        this.entityStart = this.index;\n        this.entityDecoder.startEntity(this.xmlMode\n            ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.DecodingMode.Strict\n            : this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag\n                ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.DecodingMode.Legacy\n                : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.DecodingMode.Attribute);\n    }\n    stateInEntity() {\n        const length = this.entityDecoder.write(this.buffer, this.index - this.offset);\n        // If `length` is positive, we are done with the entity.\n        if (length >= 0) {\n            this.state = this.baseState;\n            if (length === 0) {\n                this.index = this.entityStart;\n            }\n        }\n        else {\n            // Mark buffer as consumed.\n            this.index = this.offset + this.buffer.length - 1;\n        }\n    }\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    cleanup() {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    }\n    shouldContinue() {\n        return this.index < this.buffer.length + this.offset && this.running;\n    }\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    parse() {\n        while (this.shouldContinue()) {\n            const c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.BeforeSpecialT: {\n                    this.stateBeforeSpecialT(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InEntity: {\n                    this.stateInEntity();\n                    break;\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    }\n    finish() {\n        if (this.state === State.InEntity) {\n            this.entityDecoder.end();\n            this.state = this.baseState;\n        }\n        this.handleTrailingData();\n        this.cbs.onend();\n    }\n    /** Handle any trailing data. */\n    handleTrailingData() {\n        const endIndex = this.buffer.length + this.offset;\n        // If there is no remaining data, we are done.\n        if (this.sectionStart >= endIndex) {\n            return;\n        }\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    }\n    emitCodePoint(cp, consumed) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            if (this.sectionStart < this.entityStart) {\n                this.cbs.onattribdata(this.sectionStart, this.entityStart);\n            }\n            this.sectionStart = this.entityStart + consumed;\n            this.index = this.sectionStart - 1;\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            if (this.sectionStart < this.entityStart) {\n                this.cbs.ontext(this.sectionStart, this.entityStart);\n            }\n            this.sectionStart = this.entityStart + consumed;\n            this.index = this.sectionStart - 1;\n            this.cbs.ontextentity(cp, this.sectionStart);\n        }\n    }\n}\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomUtils: () => (/* reexport module object */ domutils__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   ElementType: () => (/* reexport module object */ domelementtype__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   Parser: () => (/* reexport safe */ _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser),\n/* harmony export */   QuoteType: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__.QuoteType),\n/* harmony export */   Tokenizer: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createDocumentStream: () => (/* binding */ createDocumentStream),\n/* harmony export */   createDomStream: () => (/* binding */ createDomStream),\n/* harmony export */   getFeed: () => (/* reexport safe */ domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed),\n/* harmony export */   parseDOM: () => (/* binding */ parseDOM),\n/* harmony export */   parseDocument: () => (/* binding */ parseDocument),\n/* harmony export */   parseFeed: () => (/* binding */ parseFeed)\n/* harmony export */ });\n/* harmony import */ var _Parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parser.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n\n\n\n\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM handler.\n */\nfunction parseDocument(data, options) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(undefined, options);\n    new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options).end(data);\n    return handler.root;\n}\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM handler.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed, with the resulting document.\n * @param options Optional options for the parser and DOM handler.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDocumentStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler((error) => callback(error, handler.root), options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed, with an array of root nodes.\n * @param options Optional options for the parser and DOM handler.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n * @deprecated Use `createDocumentStream` instead.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(callback, options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\n\n\n\nconst parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options = parseFeedDefaultOptions) {\n    return (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed)(parseDOM(feed, options));\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/index.js\n");

/***/ })

};
;