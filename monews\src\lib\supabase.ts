import { createClient } from '@supabase/supabase-js'

// Check if we have valid Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Use placeholder values if credentials are not set or are placeholder text
const isValidUrl = supabaseUrl && supabaseUrl !== 'your_supabase_url_here' && supabaseUrl.startsWith('https://')
const isValidKey = supabaseAnonKey && supabaseAnonKey !== 'your_supabase_anon_key_here'

const finalUrl = isValidUrl ? supabaseUrl : 'https://placeholder.supabase.co'
const finalKey = isValidKey ? supabaseAnonKey : 'placeholder-key'

export const supabase = createClient(finalUrl, finalKey)

// Flag to check if Supabase is properly configured
export const isSupabaseConfigured = isValidUrl && isValid<PERSON>ey

// Types for our database tables
export interface NewsSource {
  id: string
  name: string
  url: string
  category: 'channel' | 'agency' | 'newspaper' | 'facebook' | 'telegram'
  is_active: boolean
  last_checked: string | null
  status: 'active' | 'inactive' | 'error'
  created_at: string
  updated_at: string
}

export interface NewsArticle {
  id: string
  title: string
  content: string | null
  url: string
  source_id: string
  source_name: string
  province: string | null
  published_date: string
  scraped_at: string
  is_iraq_related: boolean
  category: string | null
  created_at: string
}

export interface Category {
  id: string
  name: string
  is_excluded: boolean
  created_at: string
}
