import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database tables
export interface NewsSource {
  id: string
  name: string
  url: string
  category: 'channel' | 'agency' | 'newspaper' | 'facebook' | 'telegram'
  is_active: boolean
  last_checked: string | null
  status: 'active' | 'inactive' | 'error'
  created_at: string
  updated_at: string
}

export interface NewsArticle {
  id: string
  title: string
  content: string | null
  url: string
  source_id: string
  source_name: string
  province: string | null
  published_date: string
  scraped_at: string
  is_iraq_related: boolean
  category: string | null
  created_at: string
}

export interface Category {
  id: string
  name: string
  is_excluded: boolean
  created_at: string
}
