'use client'

import { useState } from 'react'
import { supabase, NewsSource } from '@/lib/supabase'
import { X, Save, Globe } from 'lucide-react'

interface SourceFormProps {
  source?: NewsSource | null
  onClose: () => void
  onSave: () => void
}

export default function SourceForm({ source, onClose, onSave }: SourceFormProps) {
  const [formData, setFormData] = useState({
    name: source?.name || '',
    url: source?.url || '',
    category: source?.category || 'channel',
    is_active: source?.is_active ?? true
  })
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)

  const categories = [
    { value: 'channel', label: 'قنوات' },
    { value: 'agency', label: 'وكالات' },
    { value: 'newspaper', label: 'صحف' },
    { value: 'facebook', label: 'فيسبوك' },
    { value: 'telegram', label: 'تليغرام' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (source) {
        // Update existing source
        const { error } = await supabase
          .from('news_sources')
          .update({
            name: formData.name,
            url: formData.url,
            category: formData.category,
            is_active: formData.is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', source.id)

        if (error) throw error
      } else {
        // Create new source
        const { error } = await supabase
          .from('news_sources')
          .insert([{
            name: formData.name,
            url: formData.url,
            category: formData.category,
            is_active: formData.is_active,
            status: 'active'
          }])

        if (error) throw error
      }

      onSave()
      onClose()
    } catch (error) {
      console.error('Error saving source:', error)
      alert('حدث خطأ أثناء حفظ المصدر')
    } finally {
      setLoading(false)
    }
  }

  const testSource = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/test-source', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: formData.url })
      })

      const result = await response.json()
      
      if (result.success) {
        setTestResult('✅ المصدر يعمل بشكل صحيح')
      } else {
        setTestResult(`❌ خطأ: ${result.error}`)
      }
    } catch (error) {
      setTestResult('❌ فشل في اختبار المصدر')
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {source ? 'تعديل المصدر' : 'إضافة مصدر جديد'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اسم المصدر
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              رابط المصدر
            </label>
            <div className="flex gap-2">
              <input
                type="url"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="button"
                onClick={testSource}
                disabled={testing || !formData.url}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
              >
                {testing ? '...' : <Globe className="w-4 h-4" />}
              </button>
            </div>
            {testResult && (
              <p className="text-sm mt-1 text-gray-600">{testResult}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع المصدر
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value as any })}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="mr-2 block text-sm text-gray-900">
              مصدر نشط
            </label>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? (
                'جاري الحفظ...'
              ) : (
                <>
                  <Save className="w-4 h-4 ml-2" />
                  حفظ
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
