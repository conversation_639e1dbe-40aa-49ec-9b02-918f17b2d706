"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TriangleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"triangle-alert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,ExternalLink,Globe,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [articles, setArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedProvince, setSelectedProvince] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            fetchArticles();\n        }\n    }[\"Home.useEffect\"], [\n        selectedProvince\n    ]);\n    const fetchArticles = async ()=>{\n        setLoading(true);\n        // Check if Supabase is configured\n        if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured) {\n            console.log('Supabase not configured, using demo data');\n            // Set demo data for development\n            setArticles([\n                {\n                    id: '1',\n                    title: 'مرحباً بك في تطبيق الرصد الإعلامي',\n                    content: 'هذا مثال على خبر تجريبي. يرجى تكوين Supabase لعرض الأخبار الحقيقية.',\n                    url: '#',\n                    source_id: '1',\n                    source_name: 'مصدر تجريبي',\n                    province: 'بغداد',\n                    published_date: new Date().toISOString(),\n                    scraped_at: new Date().toISOString(),\n                    is_iraq_related: true,\n                    category: 'تجريبي',\n                    created_at: new Date().toISOString()\n                }\n            ]);\n            setLoading(false);\n            return;\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('news_articles').select('*').eq('is_iraq_related', true).order('published_date', {\n                ascending: false\n            }).limit(50);\n            if (selectedProvince) {\n                query = query.eq('province', selectedProvince);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching articles:', error);\n            } else {\n                setArticles(data || []);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const provinces = [\n        'بغداد',\n        'البصرة',\n        'نينوى',\n        'أربيل',\n        'النجف',\n        'كربلاء',\n        'الأنبار',\n        'ديالى',\n        'كركوك',\n        'بابل',\n        'ذي قار',\n        'واسط',\n        'صلاح الدين',\n        'القادسية',\n        'ميسان',\n        'المثنى',\n        'دهوك',\n        'السليمانية'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"الرصد الإعلامي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                        children: \"الأخبار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/sources\",\n                                        className: \"text-gray-600 hover:text-gray-800\",\n                                        children: \"إدارة المصادر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    !_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.isSupabaseConfigured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-400 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"تحتاج إلى تكوين قاعدة البيانات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-yellow-700\",\n                                            children: \"يرجى تكوين Supabase في ملف .env.local لعرض الأخبار الحقيقية. راجع ملف README.md للتعليمات.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"فلترة حسب المحافظة:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedProvince,\n                                    onChange: (e)=>setSelectedProvince(e.target.value),\n                                    className: \"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"جميع المحافظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        provinces.map((province)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: province,\n                                                children: province\n                                            }, province, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                        children: articles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3 line-clamp-2\",\n                                            children: article.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(new Date(article.published_date), 'dd MMMM yyyy - HH:mm', {\n                                                            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_6__.ar\n                                                        })\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this),\n                                                article.province && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        article.province\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        article.source_name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this),\n                                        article.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 text-sm mb-4 line-clamp-3\",\n                                            children: [\n                                                article.content.substring(0, 150),\n                                                \"...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: article.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                            children: [\n                                                \"قراءة المزيد\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_ExternalLink_Globe_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, this)\n                            }, article.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    !loading && articles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-lg\",\n                            children: \"لا توجد أخبار متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\monews\\\\monews\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"ITNK+Fnw0PulNixrDA/PZ97DG5k=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});