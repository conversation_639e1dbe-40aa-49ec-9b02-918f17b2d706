'use client'

import { useState, useEffect } from 'react'
import { supabase, NewsSource, isSupabaseConfigured } from '@/lib/supabase'
import { Plus, Edit, Trash2, CheckCircle, XCircle, Globe, AlertCircle, Download, RefreshCw, AlertTriangle } from 'lucide-react'
import SourceForm from '@/components/SourceForm'

export default function SourcesPage() {
  const [sources, setSources] = useState<NewsSource[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingSource, setEditingSource] = useState<NewsSource | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [scraping, setScraping] = useState(false)
  const [scrapingSource, setScrapingSource] = useState<string | null>(null)

  useEffect(() => {
    fetchSources()
  }, [selectedCategory])

  const fetchSources = async () => {
    setLoading(true)

    // Check if Supabase is configured
    if (!isSupabaseConfigured) {
      console.log('Supabase not configured, using demo data')
      // Set demo data for development
      setSources([
        {
          id: '1',
          name: 'مصدر تجريبي',
          url: 'https://example.com',
          category: 'channel',
          is_active: true,
          last_checked: new Date().toISOString(),
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ])
      setLoading(false)
      return
    }

    try {
      let query = supabase
        .from('news_sources')
        .select('*')
        .order('created_at', { ascending: false })

      if (selectedCategory) {
        query = query.eq('category', selectedCategory)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching sources:', error)
      } else {
        setSources(data || [])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    { value: 'channel', label: 'قنوات' },
    { value: 'agency', label: 'وكالات' },
    { value: 'newspaper', label: 'صحف' },
    { value: 'facebook', label: 'فيسبوك' },
    { value: 'telegram', label: 'تليغرام' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'inactive':
        return <XCircle className="w-5 h-5 text-gray-500" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />
      default:
        return <Globe className="w-5 h-5 text-blue-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط'
      case 'inactive':
        return 'غير نشط'
      case 'error':
        return 'خطأ'
      default:
        return 'غير محدد'
    }
  }

  const getCategoryText = (category: string) => {
    const cat = categories.find(c => c.value === category)
    return cat ? cat.label : category
  }

  const deleteSource = async (sourceId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصدر؟')) return

    try {
      const { error } = await supabase
        .from('news_sources')
        .delete()
        .eq('id', sourceId)

      if (error) throw error

      fetchSources()
    } catch (error) {
      console.error('Error deleting source:', error)
      alert('حدث خطأ أثناء حذف المصدر')
    }
  }

  const scrapeNews = async (sourceId?: string) => {
    setScraping(true)
    setScrapingSource(sourceId || null)

    try {
      const response = await fetch('/api/scrape-news', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sourceId })
      })

      const result = await response.json()

      if (result.success) {
        alert(`تم جلب ${result.totalArticles} خبر بنجاح`)
        fetchSources() // Refresh to update last_checked times
      } else {
        alert(`خطأ: ${result.error}`)
      }
    } catch (error) {
      console.error('Error scraping news:', error)
      alert('حدث خطأ أثناء جلب الأخبار')
    } finally {
      setScraping(false)
      setScrapingSource(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">إدارة المصادر الإخبارية</h1>
            <nav className="flex space-x-4 space-x-reverse">
              <a href="/" className="text-gray-600 hover:text-gray-800">
                الأخبار
              </a>
              <a href="/sources" className="text-blue-600 hover:text-blue-800 font-medium">
                إدارة المصادر
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Configuration Warning */}
        {!isSupabaseConfigured && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="w-5 h-5 text-yellow-400 ml-2" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  تحتاج إلى تكوين قاعدة البيانات
                </h3>
                <p className="mt-1 text-sm text-yellow-700">
                  يرجى تكوين Supabase في ملف .env.local لاستخدام جميع الميزات. راجع ملف README.md للتعليمات.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex flex-wrap gap-4 items-center justify-between mb-6">
          <div className="flex flex-wrap gap-4 items-center">
            <label className="text-sm font-medium text-gray-700">
              فلترة حسب النوع:
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع الأنواع</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => scrapeNews()}
              disabled={scraping}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
            >
              {scraping ? (
                <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 ml-2" />
              )}
              {scraping ? 'جاري الجلب...' : 'جلب الأخبار'}
            </button>

            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Plus className="w-4 h-4 ml-2" />
              إضافة مصدر جديد
            </button>
          </div>
        </div>

        {/* Sources Table */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    اسم المصدر
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر فحص
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sources.map((source) => (
                  <tr key={source.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {source.name}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {source.url}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {getCategoryText(source.category)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(source.status)}
                        <span className="mr-2 text-sm text-gray-900">
                          {getStatusText(source.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {source.last_checked 
                        ? new Date(source.last_checked).toLocaleDateString('ar-EG')
                        : 'لم يتم الفحص'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2 space-x-reverse">
                        <button
                          onClick={() => scrapeNews(source.id)}
                          disabled={scraping}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          title="جلب الأخبار من هذا المصدر"
                        >
                          {scrapingSource === source.id ? (
                            <RefreshCw className="w-4 h-4 animate-spin" />
                          ) : (
                            <Download className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => setEditingSource(source)}
                          className="text-blue-600 hover:text-blue-900"
                          title="تعديل المصدر"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteSource(source.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف المصدر"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {!loading && sources.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">لا توجد مصادر متاحة حالياً</p>
          </div>
        )}
      </main>

      {/* Add/Edit Source Form */}
      {(showAddForm || editingSource) && (
        <SourceForm
          source={editingSource}
          onClose={() => {
            setShowAddForm(false)
            setEditingSource(null)
          }}
          onSave={() => {
            fetchSources()
          }}
        />
      )}
    </div>
  )
}
