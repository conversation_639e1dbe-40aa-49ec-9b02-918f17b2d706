"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encoding-sniffer";
exports.ids = ["vendor-chunks/encoding-sniffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeStream: () => (/* binding */ DecodeStream),\n/* harmony export */   decodeBuffer: () => (/* binding */ decodeBuffer),\n/* harmony export */   getEncoding: () => (/* reexport safe */ _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var iconv_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\n/* harmony import */ var _sniffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sniffer.js */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\");\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\n/**\n * Sniff the encoding of a buffer, then decode it.\n *\n * @param buffer Buffer to be decoded\n * @param options Options for the sniffer\n * @returns The decoded buffer\n */\nfunction decodeBuffer(buffer, options) {\n    if (options === void 0) { options = {}; }\n    return iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decode(buffer, (0,_sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)(buffer, options));\n}\n/**\n * Decodes a stream of buffers into a stream of strings.\n *\n * Reads the first 1024 bytes and passes them to the sniffer. Once an encoding\n * has been determined, it passes all data to iconv-lite's stream and outputs\n * the results.\n */\nvar DecodeStream = /** @class */ (function (_super) {\n    __extends(DecodeStream, _super);\n    function DecodeStream(options) {\n        var _a;\n        var _this = _super.call(this, { decodeStrings: false, encoding: \"utf-8\" }) || this;\n        _this.buffers = [];\n        /** The iconv decode stream. If it is set, we have read more than `options.maxBytes` bytes. */\n        _this.iconv = null;\n        _this.readBytes = 0;\n        _this.sniffer = new _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.Sniffer(options);\n        _this.maxBytes = (_a = options === null || options === void 0 ? void 0 : options.maxBytes) !== null && _a !== void 0 ? _a : 1024;\n        return _this;\n    }\n    DecodeStream.prototype._transform = function (chunk, _encoding, callback) {\n        if (this.readBytes < this.maxBytes) {\n            this.sniffer.write(chunk);\n            this.readBytes += chunk.length;\n            if (this.readBytes < this.maxBytes) {\n                this.buffers.push(chunk);\n                callback();\n                return;\n            }\n        }\n        this.getIconvStream().write(chunk, callback);\n    };\n    DecodeStream.prototype.getIconvStream = function () {\n        var _this = this;\n        if (this.iconv) {\n            return this.iconv;\n        }\n        var stream = iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decodeStream(this.sniffer.encoding);\n        stream.on(\"data\", function (chunk) { return _this.push(chunk, \"utf-8\"); });\n        stream.on(\"end\", function () { return _this.push(null); });\n        this.iconv = stream;\n        for (var _i = 0, _a = this.buffers; _i < _a.length; _i++) {\n            var buffer = _a[_i];\n            stream.write(buffer);\n        }\n        this.buffers.length = 0;\n        return stream;\n    };\n    DecodeStream.prototype._flush = function (callback) {\n        this.getIconvStream().end(callback);\n    };\n    return DecodeStream;\n}(node_stream__WEBPACK_IMPORTED_MODULE_0__.Transform));\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js":
/*!***********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/sniffer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResultType: () => (/* binding */ ResultType),\n/* harmony export */   STRINGS: () => (/* binding */ STRINGS),\n/* harmony export */   Sniffer: () => (/* binding */ Sniffer),\n/* harmony export */   getEncoding: () => (/* binding */ getEncoding)\n/* harmony export */ });\n/* harmony import */ var whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! whatwg-encoding */ \"(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js\");\n\n// https://html.spec.whatwg.org/multipage/syntax.html#prescan-a-byte-stream-to-determine-its-encoding\nvar State;\n(function (State) {\n    // Before anything starts; can be any of BOM, UTF-16 XML declarations or meta tags\n    State[State[\"Begin\"] = 0] = \"Begin\";\n    // Inside of a BOM\n    State[State[\"BOM16BE\"] = 1] = \"BOM16BE\";\n    State[State[\"BOM16LE\"] = 2] = \"BOM16LE\";\n    State[State[\"BOM8\"] = 3] = \"BOM8\";\n    // XML prefix\n    State[State[\"UTF16LE_XML_PREFIX\"] = 4] = \"UTF16LE_XML_PREFIX\";\n    State[State[\"BeginLT\"] = 5] = \"BeginLT\";\n    State[State[\"UTF16BE_XML_PREFIX\"] = 6] = \"UTF16BE_XML_PREFIX\";\n    // Waiting for opening `<`\n    State[State[\"BeforeTag\"] = 7] = \"BeforeTag\";\n    // After the opening `<`\n    State[State[\"BeforeTagName\"] = 8] = \"BeforeTagName\";\n    // After `</`\n    State[State[\"BeforeCloseTagName\"] = 9] = \"BeforeCloseTagName\";\n    // Beginning of a comment\n    State[State[\"CommentStart\"] = 10] = \"CommentStart\";\n    // End of a comment\n    State[State[\"CommentEnd\"] = 11] = \"CommentEnd\";\n    // A tag name that could be `meta`\n    State[State[\"TagNameMeta\"] = 12] = \"TagNameMeta\";\n    // A tag name that is not `meta`\n    State[State[\"TagNameOther\"] = 13] = \"TagNameOther\";\n    // XML declaration\n    State[State[\"XMLDeclaration\"] = 14] = \"XMLDeclaration\";\n    State[State[\"XMLDeclarationBeforeEncoding\"] = 15] = \"XMLDeclarationBeforeEncoding\";\n    State[State[\"XMLDeclarationAfterEncoding\"] = 16] = \"XMLDeclarationAfterEncoding\";\n    State[State[\"XMLDeclarationBeforeValue\"] = 17] = \"XMLDeclarationBeforeValue\";\n    State[State[\"XMLDeclarationValue\"] = 18] = \"XMLDeclarationValue\";\n    // Anything that looks like a tag, but doesn't fit in the above categories\n    State[State[\"WeirdTag\"] = 19] = \"WeirdTag\";\n    State[State[\"BeforeAttribute\"] = 20] = \"BeforeAttribute\";\n    /*\n     * Attributes in meta tag — we compare them to our set here, and back out\n     * We care about four attributes: http-equiv, content-type, content, charset\n     */\n    State[State[\"MetaAttribHttpEquiv\"] = 21] = \"MetaAttribHttpEquiv\";\n    // The value has to be `content-type`\n    State[State[\"MetaAttribHttpEquivValue\"] = 22] = \"MetaAttribHttpEquivValue\";\n    State[State[\"MetaAttribC\"] = 23] = \"MetaAttribC\";\n    State[State[\"MetaAttribContent\"] = 24] = \"MetaAttribContent\";\n    State[State[\"MetaAttribCharset\"] = 25] = \"MetaAttribCharset\";\n    // Waiting for whitespace\n    State[State[\"MetaAttribAfterName\"] = 26] = \"MetaAttribAfterName\";\n    State[State[\"MetaContentValueQuotedBeforeEncoding\"] = 27] = \"MetaContentValueQuotedBeforeEncoding\";\n    State[State[\"MetaContentValueQuotedAfterEncoding\"] = 28] = \"MetaContentValueQuotedAfterEncoding\";\n    State[State[\"MetaContentValueQuotedBeforeValue\"] = 29] = \"MetaContentValueQuotedBeforeValue\";\n    State[State[\"MetaContentValueQuotedValueQuoted\"] = 30] = \"MetaContentValueQuotedValueQuoted\";\n    State[State[\"MetaContentValueQuotedValueUnquoted\"] = 31] = \"MetaContentValueQuotedValueUnquoted\";\n    State[State[\"MetaContentValueUnquotedBeforeEncoding\"] = 32] = \"MetaContentValueUnquotedBeforeEncoding\";\n    State[State[\"MetaContentValueUnquotedBeforeValue\"] = 33] = \"MetaContentValueUnquotedBeforeValue\";\n    State[State[\"MetaContentValueUnquotedValueQuoted\"] = 34] = \"MetaContentValueUnquotedValueQuoted\";\n    State[State[\"MetaContentValueUnquotedValueUnquoted\"] = 35] = \"MetaContentValueUnquotedValueUnquoted\";\n    State[State[\"AnyAttribName\"] = 36] = \"AnyAttribName\";\n    // After the name of an attribute, before the equals sign\n    State[State[\"AfterAttributeName\"] = 37] = \"AfterAttributeName\";\n    // After `=`\n    State[State[\"BeforeAttributeValue\"] = 38] = \"BeforeAttributeValue\";\n    State[State[\"AttributeValueQuoted\"] = 39] = \"AttributeValueQuoted\";\n    State[State[\"AttributeValueUnquoted\"] = 40] = \"AttributeValueUnquoted\";\n})(State || (State = {}));\nvar ResultType;\n(function (ResultType) {\n    // Byte order mark\n    ResultType[ResultType[\"BOM\"] = 0] = \"BOM\";\n    // User- or transport layer-defined\n    ResultType[ResultType[\"PASSED\"] = 1] = \"PASSED\";\n    // XML prefixes\n    ResultType[ResultType[\"XML_PREFIX\"] = 2] = \"XML_PREFIX\";\n    // Meta tag\n    ResultType[ResultType[\"META_TAG\"] = 3] = \"META_TAG\";\n    // XML encoding\n    ResultType[ResultType[\"XML_ENCODING\"] = 4] = \"XML_ENCODING\";\n    // Default\n    ResultType[ResultType[\"DEFAULT\"] = 5] = \"DEFAULT\";\n})(ResultType || (ResultType = {}));\nvar AttribType;\n(function (AttribType) {\n    AttribType[AttribType[\"None\"] = 0] = \"None\";\n    AttribType[AttribType[\"HttpEquiv\"] = 1] = \"HttpEquiv\";\n    AttribType[AttribType[\"Content\"] = 2] = \"Content\";\n    AttribType[AttribType[\"Charset\"] = 3] = \"Charset\";\n})(AttribType || (AttribType = {}));\nvar Chars;\n(function (Chars) {\n    Chars[Chars[\"NIL\"] = 0] = \"NIL\";\n    Chars[Chars[\"TAB\"] = 9] = \"TAB\";\n    Chars[Chars[\"LF\"] = 10] = \"LF\";\n    Chars[Chars[\"CR\"] = 13] = \"CR\";\n    Chars[Chars[\"SPACE\"] = 32] = \"SPACE\";\n    Chars[Chars[\"EXCLAMATION\"] = 33] = \"EXCLAMATION\";\n    Chars[Chars[\"DQUOTE\"] = 34] = \"DQUOTE\";\n    Chars[Chars[\"SQUOTE\"] = 39] = \"SQUOTE\";\n    Chars[Chars[\"DASH\"] = 45] = \"DASH\";\n    Chars[Chars[\"SLASH\"] = 47] = \"SLASH\";\n    Chars[Chars[\"SEMICOLON\"] = 59] = \"SEMICOLON\";\n    Chars[Chars[\"LT\"] = 60] = \"LT\";\n    Chars[Chars[\"EQUALS\"] = 61] = \"EQUALS\";\n    Chars[Chars[\"GT\"] = 62] = \"GT\";\n    Chars[Chars[\"QUESTION\"] = 63] = \"QUESTION\";\n    Chars[Chars[\"UpperA\"] = 65] = \"UpperA\";\n    Chars[Chars[\"UpperZ\"] = 90] = \"UpperZ\";\n    Chars[Chars[\"LowerA\"] = 97] = \"LowerA\";\n    Chars[Chars[\"LowerZ\"] = 122] = \"LowerZ\";\n})(Chars || (Chars = {}));\nvar SPACE_CHARACTERS = new Set([Chars.SPACE, Chars.LF, Chars.CR, Chars.TAB]);\nvar END_OF_UNQUOTED_ATTRIBUTE_VALUE = new Set([\n    Chars.SPACE,\n    Chars.LF,\n    Chars.CR,\n    Chars.TAB,\n    Chars.GT,\n]);\nfunction toUint8Array(str) {\n    var arr = new Uint8Array(str.length);\n    for (var i = 0; i < str.length; i++) {\n        arr[i] = str.charCodeAt(i);\n    }\n    return arr;\n}\nvar STRINGS = {\n    UTF8_BOM: new Uint8Array([0xef, 0xbb, 0xbf]),\n    UTF16LE_BOM: new Uint8Array([0xff, 0xfe]),\n    UTF16BE_BOM: new Uint8Array([0xfe, 0xff]),\n    UTF16LE_XML_PREFIX: new Uint8Array([0x3c, 0x0, 0x3f, 0x0, 0x78, 0x0]),\n    UTF16BE_XML_PREFIX: new Uint8Array([0x0, 0x3c, 0x0, 0x3f, 0x0, 0x78]),\n    XML_DECLARATION: toUint8Array(\"<?xml\"),\n    ENCODING: toUint8Array(\"encoding\"),\n    META: toUint8Array(\"meta\"),\n    HTTP_EQUIV: toUint8Array(\"http-equiv\"),\n    CONTENT: toUint8Array(\"content\"),\n    CONTENT_TYPE: toUint8Array(\"content-type\"),\n    CHARSET: toUint8Array(\"charset\"),\n    COMMENT_START: toUint8Array(\"<!--\"),\n    COMMENT_END: toUint8Array(\"-->\"),\n};\nfunction isAsciiAlpha(c) {\n    return ((c >= Chars.UpperA && c <= Chars.UpperZ) ||\n        (c >= Chars.LowerA && c <= Chars.LowerZ));\n}\nfunction isQuote(c) {\n    return c === Chars.DQUOTE || c === Chars.SQUOTE;\n}\nvar Sniffer = /** @class */ (function () {\n    function Sniffer(_a) {\n        var _b = _a === void 0 ? {} : _a, _c = _b.maxBytes, maxBytes = _c === void 0 ? 1024 : _c, userEncoding = _b.userEncoding, transportLayerEncodingLabel = _b.transportLayerEncodingLabel, defaultEncoding = _b.defaultEncoding;\n        /** The offset of the previous buffers. */\n        this.offset = 0;\n        this.state = State.Begin;\n        this.sectionIndex = 0;\n        this.attribType = AttribType.None;\n        /**\n         * Indicates if the `http-equiv` is `content-type`.\n         *\n         * Initially `null`, a boolean when a value is found.\n         */\n        this.gotPragma = null;\n        this.needsPragma = null;\n        this.inMetaTag = false;\n        this.encoding = \"windows-1252\";\n        this.resultType = ResultType.DEFAULT;\n        this.quoteCharacter = 0;\n        this.attributeValue = [];\n        this.maxBytes = maxBytes;\n        if (userEncoding) {\n            this.setResult(userEncoding, ResultType.PASSED);\n        }\n        if (transportLayerEncodingLabel) {\n            this.setResult(transportLayerEncodingLabel, ResultType.PASSED);\n        }\n        if (defaultEncoding) {\n            this.setResult(defaultEncoding, ResultType.DEFAULT);\n        }\n    }\n    Sniffer.prototype.setResult = function (label, type) {\n        if (this.resultType === ResultType.DEFAULT || this.resultType > type) {\n            var encoding = (0,whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__.labelToName)(label);\n            if (encoding) {\n                this.encoding =\n                    // Check if we are in a meta tag and the encoding is `x-user-defined`\n                    type === ResultType.META_TAG &&\n                        encoding === \"x-user-defined\"\n                        ? \"windows-1252\"\n                        : // Check if we are in a meta tag or xml declaration, and the encoding is UTF-16\n                            (type === ResultType.META_TAG ||\n                                type === ResultType.XML_ENCODING) &&\n                                (encoding === \"UTF-16LE\" || encoding === \"UTF-16BE\")\n                                ? \"UTF-8\"\n                                : encoding;\n                this.resultType = type;\n            }\n        }\n    };\n    Sniffer.prototype.stateBegin = function (c) {\n        switch (c) {\n            case STRINGS.UTF16BE_BOM[0]: {\n                this.state = State.BOM16BE;\n                break;\n            }\n            case STRINGS.UTF16LE_BOM[0]: {\n                this.state = State.BOM16LE;\n                break;\n            }\n            case STRINGS.UTF8_BOM[0]: {\n                this.sectionIndex = 1;\n                this.state = State.BOM8;\n                break;\n            }\n            case Chars.NIL: {\n                this.state = State.UTF16BE_XML_PREFIX;\n                this.sectionIndex = 1;\n                break;\n            }\n            case Chars.LT: {\n                this.state = State.BeginLT;\n                break;\n            }\n            default: {\n                this.state = State.BeforeTag;\n            }\n        }\n    };\n    Sniffer.prototype.stateBeginLT = function (c) {\n        if (c === Chars.NIL) {\n            this.state = State.UTF16LE_XML_PREFIX;\n            this.sectionIndex = 2;\n        }\n        else if (c === Chars.QUESTION) {\n            this.state = State.XMLDeclaration;\n            this.sectionIndex = 2;\n        }\n        else {\n            this.state = State.BeforeTagName;\n            this.stateBeforeTagName(c);\n        }\n    };\n    Sniffer.prototype.stateUTF16BE_XML_PREFIX = function (c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16BE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16BE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16be\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    };\n    Sniffer.prototype.stateUTF16LE_XML_PREFIX = function (c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16LE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16LE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16le\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    };\n    Sniffer.prototype.stateBOM16LE = function (c) {\n        if (c === STRINGS.UTF16LE_BOM[1]) {\n            this.setResult(\"utf-16le\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    };\n    Sniffer.prototype.stateBOM16BE = function (c) {\n        if (c === STRINGS.UTF16BE_BOM[1]) {\n            this.setResult(\"utf-16be\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    };\n    Sniffer.prototype.stateBOM8 = function (c) {\n        if (this.advanceSection(STRINGS.UTF8_BOM, c) &&\n            this.sectionIndex === STRINGS.UTF8_BOM.length) {\n            this.setResult(\"utf-8\", ResultType.BOM);\n        }\n    };\n    Sniffer.prototype.stateBeforeTag = function (c) {\n        if (c === Chars.LT) {\n            this.state = State.BeforeTagName;\n            this.inMetaTag = false;\n        }\n    };\n    /**\n     * We have seen a `<`, and now have to figure out what to do.\n     *\n     * Options:\n     *  - `<meta`\n     *  - Any other tag\n     *  - A closing tag\n     *  - `<!--`\n     *  - An XML declaration\n     *\n     */\n    Sniffer.prototype.stateBeforeTagName = function (c) {\n        if (isAsciiAlpha(c)) {\n            if ((c | 0x20) === STRINGS.META[0]) {\n                this.sectionIndex = 1;\n                this.state = State.TagNameMeta;\n            }\n            else {\n                this.state = State.TagNameOther;\n            }\n        }\n        else\n            switch (c) {\n                case Chars.SLASH: {\n                    this.state = State.BeforeCloseTagName;\n                    break;\n                }\n                case Chars.EXCLAMATION: {\n                    this.state = State.CommentStart;\n                    this.sectionIndex = 2;\n                    break;\n                }\n                case Chars.QUESTION: {\n                    this.state = State.WeirdTag;\n                    break;\n                }\n                default: {\n                    this.state = State.BeforeTag;\n                    this.stateBeforeTag(c);\n                }\n            }\n    };\n    Sniffer.prototype.stateBeforeCloseTagName = function (c) {\n        this.state = isAsciiAlpha(c)\n            ? // Switch to `TagNameOther`; the HTML spec allows attributes here as well.\n                State.TagNameOther\n            : State.WeirdTag;\n    };\n    Sniffer.prototype.stateCommentStart = function (c) {\n        if (this.advanceSection(STRINGS.COMMENT_START, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_START.length) {\n                this.state = State.CommentEnd;\n                // The -- of the comment start can be part of the end.\n                this.sectionIndex = 2;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    };\n    Sniffer.prototype.stateCommentEnd = function (c) {\n        if (this.advanceSection(STRINGS.COMMENT_END, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_END.length) {\n                this.state = State.BeforeTag;\n            }\n        }\n        else if (c === Chars.DASH) {\n            /*\n             * If we are here, we know we expected a `>` above.\n             * Set this to 2, to support many dashes before the closing `>`.\n             */\n            this.sectionIndex = 2;\n        }\n    };\n    /**\n     * Any section starting with `<!`, `<?`, `</`, without being a closing tag or comment.\n     */\n    Sniffer.prototype.stateWeirdTag = function (c) {\n        if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    };\n    /**\n     * Advances the section, ignoring upper/lower case.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    Sniffer.prototype.advanceSectionIC = function (section, c) {\n        return this.advanceSection(section, c | 0x20);\n    };\n    /**\n     * Advances the section.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    Sniffer.prototype.advanceSection = function (section, c) {\n        if (section[this.sectionIndex] === c) {\n            this.sectionIndex++;\n            return true;\n        }\n        this.sectionIndex = 0;\n        return false;\n    };\n    Sniffer.prototype.stateTagNameMeta = function (c) {\n        if (this.sectionIndex < STRINGS.META.length) {\n            if (this.advanceSectionIC(STRINGS.META, c)) {\n                return;\n            }\n        }\n        else if (SPACE_CHARACTERS.has(c)) {\n            this.inMetaTag = true;\n            this.gotPragma = null;\n            this.needsPragma = null;\n            this.state = State.BeforeAttribute;\n            return;\n        }\n        this.state = State.TagNameOther;\n        // Reconsume in case there is a `>`.\n        this.stateTagNameOther(c);\n    };\n    Sniffer.prototype.stateTagNameOther = function (c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    };\n    Sniffer.prototype.stateBeforeAttribute = function (c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (this.inMetaTag) {\n            var lower = c | 0x20;\n            if (lower === STRINGS.HTTP_EQUIV[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribHttpEquiv;\n                return;\n            }\n            else if (lower === STRINGS.CHARSET[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribC;\n                return;\n            }\n        }\n        this.state =\n            c === Chars.SLASH || c === Chars.GT\n                ? State.BeforeTag\n                : State.AnyAttribName;\n    };\n    Sniffer.prototype.handleMetaAttrib = function (c, section, type) {\n        if (this.advanceSectionIC(section, c)) {\n            if (this.sectionIndex === section.length) {\n                this.attribType = type;\n                this.state = State.MetaAttribAfterName;\n            }\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    };\n    Sniffer.prototype.stateMetaAttribHttpEquiv = function (c) {\n        this.handleMetaAttrib(c, STRINGS.HTTP_EQUIV, AttribType.HttpEquiv);\n    };\n    Sniffer.prototype.stateMetaAttribC = function (c) {\n        var lower = c | 0x20;\n        if (lower === STRINGS.CHARSET[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribCharset;\n        }\n        else if (lower === STRINGS.CONTENT[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribContent;\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    };\n    Sniffer.prototype.stateMetaAttribCharset = function (c) {\n        this.handleMetaAttrib(c, STRINGS.CHARSET, AttribType.Charset);\n    };\n    Sniffer.prototype.stateMetaAttribContent = function (c) {\n        this.handleMetaAttrib(c, STRINGS.CONTENT, AttribType.Content);\n    };\n    Sniffer.prototype.stateMetaAttribAfterName = function (c) {\n        if (SPACE_CHARACTERS.has(c) || c === Chars.EQUALS) {\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    };\n    Sniffer.prototype.stateAnyAttribName = function (c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.attribType = AttribType.None;\n            this.state = State.AfterAttributeName;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n    };\n    Sniffer.prototype.stateAfterAttributeName = function (c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else {\n            this.state = State.BeforeAttribute;\n            this.stateBeforeAttribute(c);\n        }\n    };\n    Sniffer.prototype.stateBeforeAttributeValue = function (c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        this.attributeValue.length = 0;\n        this.sectionIndex = 0;\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state =\n                this.attribType === AttribType.Content\n                    ? State.MetaContentValueQuotedBeforeEncoding\n                    : this.attribType === AttribType.HttpEquiv\n                        ? State.MetaAttribHttpEquivValue\n                        : State.AttributeValueQuoted;\n        }\n        else if (this.attribType === AttribType.Content) {\n            this.state = State.MetaContentValueUnquotedBeforeEncoding;\n            this.stateMetaContentValueUnquotedBeforeEncoding(c);\n        }\n        else if (this.attribType === AttribType.HttpEquiv) {\n            // We use `quoteCharacter = 0` to signify that the value is unquoted.\n            this.quoteCharacter = 0;\n            this.sectionIndex = 0;\n            this.state = State.MetaAttribHttpEquivValue;\n            this.stateMetaAttribHttpEquivValue(c);\n        }\n        else {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n    };\n    // The value has to be `content-type`\n    Sniffer.prototype.stateMetaAttribHttpEquivValue = function (c) {\n        if (this.sectionIndex === STRINGS.CONTENT_TYPE.length) {\n            if (this.quoteCharacter === 0\n                ? END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)\n                : c === this.quoteCharacter) {\n                if (this.needsPragma !== null) {\n                    this.setResult(this.needsPragma, ResultType.META_TAG);\n                }\n                else if (this.gotPragma === null) {\n                    this.gotPragma = true;\n                }\n                this.state = State.BeforeAttribute;\n                return;\n            }\n        }\n        else if (this.advanceSectionIC(STRINGS.CONTENT_TYPE, c)) {\n            return;\n        }\n        this.gotPragma = false;\n        if (this.quoteCharacter === 0) {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n    };\n    Sniffer.prototype.handleMetaContentValue = function () {\n        if (this.attributeValue.length === 0)\n            return;\n        var encoding = String.fromCharCode.apply(String, this.attributeValue);\n        if (this.gotPragma) {\n            this.setResult(encoding, ResultType.META_TAG);\n        }\n        else if (this.needsPragma === null) {\n            // Don't override a previous result.\n            this.needsPragma = encoding;\n        }\n        this.attributeValue.length = 0;\n    };\n    Sniffer.prototype.handleAttributeValue = function () {\n        if (this.attribType === AttribType.Charset) {\n            this.setResult(String.fromCharCode.apply(String, this.attributeValue), ResultType.META_TAG);\n        }\n    };\n    Sniffer.prototype.stateAttributeValueUnquoted = function (c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.handleAttributeValue();\n            this.state = State.BeforeTag;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.findMetaContentEncoding = function (c) {\n        if (this.advanceSectionIC(STRINGS.CHARSET, c)) {\n            if (this.sectionIndex === STRINGS.CHARSET.length) {\n                return true;\n            }\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.CHARSET[0]);\n        }\n        return false;\n    };\n    Sniffer.prototype.stateMetaContentValueUnquotedBeforeEncoding = function (c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (this.sectionIndex === STRINGS.CHARSET.length) {\n            if (c === Chars.EQUALS) {\n                this.state = State.MetaContentValueUnquotedBeforeValue;\n            }\n        }\n        else {\n            this.findMetaContentEncoding(c);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueUnquotedBeforeValue = function (c) {\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state = State.MetaContentValueUnquotedValueQuoted;\n        }\n        else if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Can't have spaces here, as it would no longer be part of the attribute value.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.MetaContentValueUnquotedValueUnquoted;\n            this.stateMetaContentValueUnquotedValueUnquoted(c);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueUnquotedValueQuoted = function (c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Quotes weren't matched, so we're done.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (c === this.quoteCharacter) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n        }\n        else {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueUnquotedValueUnquoted = function (c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueQuotedValueUnquoted = function (c) {\n        if (isQuote(c) || SPACE_CHARACTERS.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            // We are done with the value, but might not be at the end of the attribute\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueQuotedValueQuoted = function (c) {\n        if (isQuote(c)) {\n            // We have reached the end of our value.\n            if (c !== this.quoteCharacter) {\n                // Only handle the value if inner quotes were matched.\n                this.handleMetaContentValue();\n            }\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueQuotedBeforeEncoding = function (c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (this.findMetaContentEncoding(c)) {\n            this.state = State.MetaContentValueQuotedAfterEncoding;\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueQuotedAfterEncoding = function (c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.MetaContentValueQuotedBeforeValue;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            // Look for the next encoding\n            this.state = State.MetaContentValueQuotedBeforeEncoding;\n            this.stateMetaContentValueQuotedBeforeEncoding(c);\n        }\n    };\n    Sniffer.prototype.stateMetaContentValueQuotedBeforeValue = function (c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (isQuote(c)) {\n            this.state = State.MetaContentValueQuotedValueQuoted;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            this.state = State.MetaContentValueQuotedValueUnquoted;\n            this.stateMetaContentValueQuotedValueUnquoted(c);\n        }\n    };\n    Sniffer.prototype.stateAttributeValueQuoted = function (c) {\n        if (c === this.quoteCharacter) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    // Read STRINGS.XML_DECLARATION\n    Sniffer.prototype.stateXMLDeclaration = function (c) {\n        if (this.advanceSection(STRINGS.XML_DECLARATION, c)) {\n            if (this.sectionIndex === STRINGS.XML_DECLARATION.length) {\n                this.sectionIndex = 0;\n                this.state = State.XMLDeclarationBeforeEncoding;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n        }\n    };\n    Sniffer.prototype.stateXMLDeclarationBeforeEncoding = function (c) {\n        if (this.advanceSection(STRINGS.ENCODING, c)) {\n            if (this.sectionIndex === STRINGS.ENCODING.length) {\n                this.state = State.XMLDeclarationAfterEncoding;\n            }\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.ENCODING[0]);\n        }\n    };\n    Sniffer.prototype.stateXMLDeclarationAfterEncoding = function (c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.XMLDeclarationBeforeValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    };\n    Sniffer.prototype.stateXMLDeclarationBeforeValue = function (c) {\n        if (isQuote(c)) {\n            this.attributeValue.length = 0;\n            this.state = State.XMLDeclarationValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    };\n    Sniffer.prototype.stateXMLDeclarationValue = function (c) {\n        if (isQuote(c)) {\n            this.setResult(String.fromCharCode.apply(String, this.attributeValue), ResultType.XML_ENCODING);\n            this.state = State.WeirdTag;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c <= Chars.SPACE) {\n            this.state = State.WeirdTag;\n        }\n        else {\n            this.attributeValue.push(c | 0x20);\n        }\n    };\n    Sniffer.prototype.write = function (buffer) {\n        var index = 0;\n        for (; index < buffer.length && this.offset + index < this.maxBytes; index++) {\n            var c = buffer[index];\n            switch (this.state) {\n                case State.Begin: {\n                    this.stateBegin(c);\n                    break;\n                }\n                case State.BOM16BE: {\n                    this.stateBOM16BE(c);\n                    break;\n                }\n                case State.BOM16LE: {\n                    this.stateBOM16LE(c);\n                    break;\n                }\n                case State.BOM8: {\n                    this.stateBOM8(c);\n                    break;\n                }\n                case State.UTF16LE_XML_PREFIX: {\n                    this.stateUTF16LE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeginLT: {\n                    this.stateBeginLT(c);\n                    break;\n                }\n                case State.UTF16BE_XML_PREFIX: {\n                    this.stateUTF16BE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeforeTag: {\n                    // Optimization: Skip all characters until we find a `<`\n                    var idx = buffer.indexOf(Chars.LT, index);\n                    if (idx < 0) {\n                        // We are done with this buffer. Stay in the state and try on the next one.\n                        index = buffer.length;\n                    }\n                    else {\n                        index = idx;\n                        this.stateBeforeTag(Chars.LT);\n                    }\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.BeforeCloseTagName: {\n                    this.stateBeforeCloseTagName(c);\n                    break;\n                }\n                case State.CommentStart: {\n                    this.stateCommentStart(c);\n                    break;\n                }\n                case State.CommentEnd: {\n                    this.stateCommentEnd(c);\n                    break;\n                }\n                case State.TagNameMeta: {\n                    this.stateTagNameMeta(c);\n                    break;\n                }\n                case State.TagNameOther: {\n                    this.stateTagNameOther(c);\n                    break;\n                }\n                case State.XMLDeclaration: {\n                    this.stateXMLDeclaration(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeEncoding: {\n                    this.stateXMLDeclarationBeforeEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationAfterEncoding: {\n                    this.stateXMLDeclarationAfterEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeValue: {\n                    this.stateXMLDeclarationBeforeValue(c);\n                    break;\n                }\n                case State.XMLDeclarationValue: {\n                    this.stateXMLDeclarationValue(c);\n                    break;\n                }\n                case State.WeirdTag: {\n                    this.stateWeirdTag(c);\n                    break;\n                }\n                case State.BeforeAttribute: {\n                    this.stateBeforeAttribute(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquiv: {\n                    this.stateMetaAttribHttpEquiv(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquivValue: {\n                    this.stateMetaAttribHttpEquivValue(c);\n                    break;\n                }\n                case State.MetaAttribC: {\n                    this.stateMetaAttribC(c);\n                    break;\n                }\n                case State.MetaAttribContent: {\n                    this.stateMetaAttribContent(c);\n                    break;\n                }\n                case State.MetaAttribCharset: {\n                    this.stateMetaAttribCharset(c);\n                    break;\n                }\n                case State.MetaAttribAfterName: {\n                    this.stateMetaAttribAfterName(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeEncoding: {\n                    this.stateMetaContentValueQuotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedAfterEncoding: {\n                    this.stateMetaContentValueQuotedAfterEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeValue: {\n                    this.stateMetaContentValueQuotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueQuoted: {\n                    this.stateMetaContentValueQuotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueUnquoted: {\n                    this.stateMetaContentValueQuotedValueUnquoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeEncoding: {\n                    this.stateMetaContentValueUnquotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeValue: {\n                    this.stateMetaContentValueUnquotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueQuoted: {\n                    this.stateMetaContentValueUnquotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueUnquoted: {\n                    this.stateMetaContentValueUnquotedValueUnquoted(c);\n                    break;\n                }\n                case State.AnyAttribName: {\n                    this.stateAnyAttribName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.AttributeValueQuoted: {\n                    this.stateAttributeValueQuoted(c);\n                    break;\n                }\n                default: {\n                    // (State.AttributeValueUnquoted)\n                    this.stateAttributeValueUnquoted(c);\n                }\n            }\n        }\n        this.offset += index;\n    };\n    return Sniffer;\n}());\n\n/** Get the encoding for the passed buffer. */\nfunction getEncoding(buffer, options) {\n    var sniffer = new Sniffer(options);\n    sniffer.write(buffer);\n    return sniffer.encoding;\n}\n//# sourceMappingURL=sniffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\n");

/***/ })

};
;