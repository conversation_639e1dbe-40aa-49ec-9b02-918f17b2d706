"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domhandler";
exports.ids = ["vendor-chunks/domhandler"];
exports.modules = {

/***/ "(rsc)/./node_modules/domhandler/lib/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA),\n/* harmony export */   Comment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment),\n/* harmony export */   DataNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.DataNode),\n/* harmony export */   Document: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Document),\n/* harmony export */   DomHandler: () => (/* binding */ DomHandler),\n/* harmony export */   Element: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Element),\n/* harmony export */   Node: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Node),\n/* harmony export */   NodeWithChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction),\n/* harmony export */   Text: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Text),\n/* harmony export */   cloneNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.cloneNode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.hasChildren),\n/* harmony export */   isCDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isComment),\n/* harmony export */   isDirective: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDirective),\n/* harmony export */   isDocument: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/esm/node.js\");\n\n\n\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nclass DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag : undefined;\n        const element = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(\"\");\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/esm/node.js":
/*!*************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* binding */ CDATA),\n/* harmony export */   Comment: () => (/* binding */ Comment),\n/* harmony export */   DataNode: () => (/* binding */ DataNode),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeWithChildren: () => (/* binding */ NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* binding */ ProcessingInstruction),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode),\n/* harmony export */   hasChildren: () => (/* binding */ hasChildren),\n/* harmony export */   isCDATA: () => (/* binding */ isCDATA),\n/* harmony export */   isComment: () => (/* binding */ isComment),\n/* harmony export */   isDirective: () => (/* binding */ isDirective),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isTag: () => (/* binding */ isTag),\n/* harmony export */   isText: () => (/* binding */ isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nclass Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nclass DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nclass Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nclass Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nclass ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nclass NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nclass CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nclass Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nclass Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Script\n        : name === \"style\"\n            ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Style\n            : domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0,domelementtype__WEBPACK_IMPORTED_MODULE_0__.isTag)(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/node.js\n");

/***/ })

};
;